"""
Simple Chatbot using Gemini AI
Handles FAQ questions based on intent.json file
"""

import json
import os
import google.generativeai as genai


class SimpleChatbot:
    def __init__(self, gemini_api_key):
        """Initialize the chatbot with Gemini API key"""
        # Configure Gemini
        genai.configure(api_key=gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Load intents from JSON file
        self.intents = self.load_intents()
        
        print("✅ Simple Chatbot initialized successfully!")

    def load_intents(self):
        """Load intents from the JSON file"""
        try:
            with open('chatbot/data/intents.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ Loaded {len(data['intents'])} intents")
            return data['intents']
        except Exception as e:
            print(f"❌ Error loading intents: {e}")
            return []

    def get_response(self, user_question):
        """Get response for user question"""
        try:
            # Create a prompt with all intents
            intents_text = ""
            for intent in self.intents:
                intents_text += f"""
Intent: {intent['page_id']}
Description: {intent['description']}
Keywords: {', '.join(intent['keywords'])}
Response: {intent['response']}
---
"""

            prompt = f"""
You are a helpful assistant for a property management system. 

Here are the available FAQ topics and their answers:
{intents_text}

User Question: "{user_question}"

Instructions:
1. Look at the user question and see if it matches any of the FAQ topics above
2. If it matches, provide the corresponding response from the FAQ
3. If it doesn't match exactly, try to find the closest related topic
4. If no topic matches, politely say you don't have information about that topic

Provide a helpful, friendly response:
"""

            # Get response from Gemini
            response = self.model.generate_content(prompt)
            return response.text.strip()
            
        except Exception as e:
            return f"Sorry, I encountered an error: {str(e)}"

    def chat(self):
        """Start interactive chat"""
        print("\n🤖 Simple Chatbot - FAQ Assistant")
        print("Ask me about property management!")
        print("Type 'quit' to exit\n")
        
        while True:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not user_input:
                continue
                
            print("Bot:", self.get_response(user_input))
            print()


def main():
    """Main function to run the chatbot"""
    
    # Get Gemini API key
    api_key = os.getenv('GEMINI_API_KEY')
    
    if not api_key:
        print("❌ Please set your GEMINI_API_KEY environment variable")
        print("Get your API key from: https://makersuite.google.com/app/apikey")
        return
    
    try:
        # Create and start chatbot
        chatbot = SimpleChatbot(api_key)
        chatbot.chat()
        
    except Exception as e:
        print(f"❌ Error starting chatbot: {e}")


if __name__ == "__main__":
    main()
